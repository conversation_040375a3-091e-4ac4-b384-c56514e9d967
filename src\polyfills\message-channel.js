/**
 * MessageChannel polyfill for Cloudflare Workers
 * 解决 React SSR 在 Workers 环境中的兼容性问题
 */

// 检查是否在 Workers 环境中且缺少 MessageChannel
if (typeof globalThis !== 'undefined' && typeof globalThis.MessageChannel === 'undefined') {
  // 简单的 MessageChannel polyfill
  class MessageChannelPolyfill {
    constructor() {
      this.port1 = new MessagePortPolyfill();
      this.port2 = new MessagePortPolyfill();
      
      // 连接两个端口
      this.port1._otherPort = this.port2;
      this.port2._otherPort = this.port1;
    }
  }
  
  class MessagePortPolyfill {
    constructor() {
      this._listeners = [];
      this._otherPort = null;
    }
    
    postMessage(data) {
      if (this._otherPort) {
        // 异步触发消息事件
        setTimeout(() => {
          this._otherPort._listeners.forEach(listener => {
            try {
              listener({ data });
            } catch (error) {
              console.warn('MessagePort listener error:', error);
            }
          });
        }, 0);
      }
    }
    
    addEventListener(type, listener) {
      if (type === 'message') {
        this._listeners.push(listener);
      }
    }
    
    removeEventListener(type, listener) {
      if (type === 'message') {
        const index = this._listeners.indexOf(listener);
        if (index > -1) {
          this._listeners.splice(index, 1);
        }
      }
    }
    
    start() {
      // MessagePort.start() 方法，在这个简单实现中不需要做任何事
    }
    
    close() {
      this._listeners = [];
      this._otherPort = null;
    }
  }
  
  // 将 polyfill 添加到全局对象
  globalThis.MessageChannel = MessageChannelPolyfill;
  globalThis.MessagePort = MessagePortPolyfill;
  
  console.log('✅ MessageChannel polyfill loaded for Workers environment');
}
