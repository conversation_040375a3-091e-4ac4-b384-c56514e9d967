---
/**
 * Header组件 (Header.astro)
 * 网站的顶部导航栏，实现 Astro+React 的高效配合
 * 
 * 框架配合：
 * - Astro：负责页面结构和静态内容渲染
 * - React：通过岛屿组件(Islands)实现交互功能
 * 
 * 主要功能：
 * 1. 提供网站品牌标识和导航入口
 * 2. 集成 React 岛屿组件实现搜索功能 (client:idle 延迟加载)
 * 3. 集成 React 岛屿组件实现主题切换 (client:idle 延迟加载)
 * 4. 控制侧边栏的显示/隐藏
 */
// 导入 React 岛屿组件 - 使用 .jsx 扩展名直接导入 React 组件
import SearchIsland from '../Island/searchlsland.jsx'; // 搜索功能岛屿
import ThemeIsland from '../Island/ThemeIsland.jsx';  // 主题切换岛屿
import LogoName from '../components/LogoName.astro';  // Logo和名称组件
---
<header class="fixed top-0 left-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg shadow-md z-50 transition-all duration-300" id="main-header" style="transform: translateY(0); transition: transform 0.3s ease-in-out;">
  <div class="container mx-auto px-4 py-3 flex justify-between items-center">
    <div class="flex items-center">
      <button id="sidebar-toggle" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all mr-4" aria-label="切换侧边栏">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
      <div class="flex items-center">
        <LogoName showSubtitle={true} />
      </div>
    </div>
    <div class="flex items-center space-x-4">
      <button id="search-toggle" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all" aria-label="搜索网站">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </button>
      <button id="theme-toggle" onclick="window.toggleTheme && window.toggleTheme()" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all" aria-label="切换亮色/暗色主题">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 block dark:hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 hidden dark:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      </button>
      <!-- github图标,跳转链接记得修改 -->
      <a href="https://github.com/zywe03" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all" aria-label="GitHub仓库">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
      </a>
    </div>
  </div>
</header>
<SearchIsland client:idle />
<ThemeIsland client:idle />
<script>
  document.addEventListener('DOMContentLoaded', () => {
    let lastScrollY = window.scrollY;
    let scrollDirection = 'up';
    let scrollTimer: ReturnType<typeof setTimeout> | null = null; 
    window.addEventListener('scroll', () => {
      const header = document.getElementById('main-header');
      if (!header) return; 
      const currentScrollY = window.scrollY;
      if (currentScrollY > lastScrollY) {
        if (currentScrollY > 100) { 
          header.style.transform = 'translateY(-100%)';
        }
        scrollDirection = 'down';
      } else {
        header.style.transform = 'translateY(0)';
        scrollDirection = 'up';
      }
      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }
      scrollTimer = setTimeout(() => {
        if (scrollDirection === 'down') {
          header.style.transform = 'translateY(0)';
        }
        scrollDirection = 'up';
      }, 1500);
      lastScrollY = currentScrollY;
    });
    window.dispatchEvent(new Event('scroll'));
  });
</script>
