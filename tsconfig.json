{"extends": "astro/tsconfigs/base", "include": [".astro/types.d.ts", "**/*"], "exclude": ["dist", "icon-system"], "compilerOptions": {"jsx": "preserve", "jsxImportSource": "astro", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noImplicitAny": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}