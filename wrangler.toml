# Cloudflare Workers 专用配置文件
# CloudNav 导航站 - 纯 Workers 部署架构
# 详细配置说明请参考: docs/DEPLOYMENT.md

name = "cloudnav-navigation"
main = "./dist/_worker.js/index.js"  # Workers 入口文件
compatibility_date = "2025-06-19"
compatibility_flags = ["nodejs_compat"]  # 启用 Node.js 兼容性

# Workers 资源限制配置
[limits]
cpu_ms = 50  # CPU 时间限制 (毫秒) - 优化响应速度

# KV 存储命名空间绑定 - Workers 数据持久化
[[kv_namespaces]]
binding = "BOOKMARKS_KV"  # 书签数据存储
id = "your_bookmarks_kv_namespace_id"  # 运行 npm run setup 自动创建
preview_id = "your_bookmarks_kv_preview_id"  # 预览环境 KV

[[kv_namespaces]]
binding = "SESSION"  # Astro 会话管理
id = "your_session_kv_namespace_id"  # 运行 npm run setup 自动创建
preview_id = "your_session_kv_preview_id"  # 预览环境 KV

# 环境变量配置
[vars]
# 管理功能开关
ENABLE_ADMIN = "true"
# 数据源模式: "static" 或 "kv"
DATA_SOURCE = "kv"
# 网站基础信息
SITE_NAME = "Cloudnav 导航站"
SITE_DESCRIPTION = "智能化的个人导航站，支持书签管理、AI 分类和数据统计"

# 开发环境配置
[env.development]
[env.development.vars]
ENABLE_ADMIN = "true"
DATA_SOURCE = "static"  # 开发环境使用静态数据
DEBUG = "true"

# 生产环境配置
[env.production]
[env.production.vars]
ENABLE_ADMIN = "false"  # 生产环境默认关闭管理功能
DATA_SOURCE = "kv"
# 生产环境安全配置
PUBLIC_ADMIN_PASSWORD_HASH = ""  # 管理员密码哈希，运行 npm run setup 生成
GEMINI_API_KEY = ""  # Gemini AI API 密钥（可选）

# 预览环境配置
[env.preview]
[env.preview.vars]
ENABLE_ADMIN = "true"
DATA_SOURCE = "kv"

# Workers Assets 静态资源配置
# 通过 Workers 直接提供静态文件，无需 Pages
[assets]
binding = "ASSETS"
directory = "./dist"  # 构建输出目录

# Workers 路由配置 - 处理所有请求
[[routes]]
pattern = "/*"  # 匹配所有路径
zone_name = "your-domain.com"  # 替换为您的域名

# Workers 可观测性配置
[observability]
enabled = true  # 启用性能监控和日志

# Workers 构建配置
[build]
command = "npm run build"  # Astro 构建命令
cwd = "."
watch_dir = "src"  # 监听源码变化

# 本地开发环境配置
# 注意：miniflare 配置在新版本 wrangler 中已内置，无需显式配置