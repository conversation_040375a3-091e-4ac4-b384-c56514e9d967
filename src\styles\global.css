@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --animation-duration: 300ms;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --color-bg-primary: #f2f2f5;         
    --color-bg-secondary: #ffffff;       
    --color-text-primary: #1d1d1f;       
    --color-text-secondary: #58585b;     
    --color-border-primary: rgba(0, 0, 0, 0.08); 
    --color-primary-accent: theme('colors.blue.600'); 
    --color-secondary-accent: theme('colors.gray.200'); 
    --transition-theme: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
  html.dark {
    --color-bg-primary: #1e1e24;         
    --color-bg-secondary: theme('colors.gray.800'); 
    --color-text-primary: #f5f5f7;       
    --color-text-secondary: theme('colors.gray.400');
    --color-border-primary: rgba(255, 255, 255, 0.1);
    --color-primary-accent: theme('colors.blue.500');
    --color-secondary-accent: theme('colors.gray.700');
  }
  /**
   * 简约风格字体设置
   * 采用简约系统字体堆栈，并启用字体平滑化渲染
   */
  body {
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "SF Pro Display", "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: -0.015em;
    text-rendering: optimizeLegibility;
  }
  /**
   * 导航栏滚动效果
   * 实现平滑的导航栏滚动过渡，增强用户体验
   */
  #main-header {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
  }
  .header-scrolled {
    transform: translateY(0);
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
    box-shadow: var(--shadow-sm);
  }
  /**
   * 卡片交互效果
   * 简约风格的微妙悬停效果，增强可发现性
   */
  .card:hover #hover-info {
    display: flex !important;
    opacity: 1 !important;
  }
  /**
   * 文本截断工具类
   * 提供多行文本截断功能，保持界面整洁
   */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;  
    overflow: hidden;
  }
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;  
    overflow: hidden;
  }
  /**
   * 基础样式设置
   * 使用柔和的背景颜色，减少白色的强度
   */
  body {
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: var(--transition-theme);
    line-height: 1.5;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }
}
/**
 * 组件层样式
 * 定义常用UI组件的样式，采用简约系统的精致风格
 */
@layer components {
  /**
   * 卡片组件
   * 带有细腻阴影和圆角的卡片设计
   */
  .card {
    @apply rounded-lg overflow-hidden transition-all duration-300;
    background-color: var(--color-bg-secondary);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--color-border-primary);
    transition: var(--transition-theme), transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), box-shadow 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  }
  .card-hover {
    @apply hover:scale-[1.02] hover:-translate-y-1;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  }
  /**
   * 按钮组件
   * 简约的按钮，带有精细的过渡效果
   */
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-300;
    letter-spacing: -0.01em;
    font-weight: var(--font-weight-medium);
  }
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  .btn-secondary {
    @apply text-gray-800 hover:bg-gray-300; 
    background-color: var(--color-secondary-accent);
    color: var(--color-text-primary); 
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: var(--transition-theme), box-shadow 0.3s ease;
  }
  html.dark .btn-secondary {
    color: var(--color-text-primary); 
  }
}
.sidebar-enter {
  animation: sidebarEnter 0.3s ease-in-out forwards;
}
.sidebar-leave {
  animation: sidebarLeave 0.3s ease-in-out forwards;
}
@keyframes sidebarEnter {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes sidebarLeave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
.card-description {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.card:hover .card-description {
  opacity: 1;
  transform: translateY(0);
}
