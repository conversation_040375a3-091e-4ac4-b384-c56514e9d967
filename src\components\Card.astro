---
interface Props {
  title: string;       
  description: string; 
  shortDesc: string;   
  url: string;         
  icon?: string;       
  category: string;    
}
import { categories } from '../data/navLinks.js';
const { title, description, shortDesc, url, icon, category } = Astro.props;
const siteIconFromProps = icon;
const categoryObject = categories.find(cat => cat.id === category);
const categoryIconPath = categoryObject ? categoryObject.icon : undefined;
---
<style is:inline>
  .card {
    background-image: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .card::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1; 
    border-radius: inherit; 
    opacity: 0; 
    border: 1px solid transparent; 
    pointer-events: none; 
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .dark .card {
    background-image: linear-gradient(135deg, #1e293b 0%, #1a2234 100%);
  }
  .card-wrapper:hover .card {
    background-image: linear-gradient(135deg, #ffffff 0%, #f9f9fa 100%);
    transform: translateY(-5px);
  }
  .card-wrapper:hover .card::before {
    opacity: 1;
    border-color: #7C3AED; 
    box-shadow: 
      0 0 0 1px #7C3AED,
      0 0 7px 2px rgba(124, 58, 237, 0.7),  
      0 0 12px 4px rgba(79, 70, 229, 0.45), 
      0 0 18px 6px rgba(0, 200, 200, 0.25), 
      0 0 22px 7px rgba(50, 205, 50, 0.1);
  }
  .dark .card-wrapper:hover .card {
    background-image: linear-gradient(135deg, #1e293b 0%, #162032 100%);
    transform: translateY(-5px);
  }
  .dark .card-wrapper:hover .card::before {
    opacity: 1;
    border-color: #7C3AED; 
    box-shadow: 
      0 0 0 1px #7C3AED,
      0 0 8px 2px rgba(124, 58, 237, 0.85), 
      0 0 14px 4px rgba(79, 70, 229, 0.65), 
      0 0 20px 6px rgba(0, 255, 255, 0.5),   
      0 0 28px 8px rgba(60, 179, 113, 0.3);  
    animation: glow-pulse 1.8s infinite alternate ease-in-out;
  }
  @keyframes glow-pulse {
    0% {
      box-shadow: 
        0 0 0 1px #7C3AED,
        0 0 8px 2px rgba(124, 58, 237, 0.85),
        0 0 14px 4px rgba(79, 70, 229, 0.65),
        0 0 20px 6px rgba(0, 255, 255, 0.5),
        0 0 28px 8px rgba(60, 179, 113, 0.3);
    }
    100% {
      box-shadow: 
        0 0 0 1px #7C3AED, 
        0 0 10px 3px rgba(124, 58, 237, 0.9),  
        0 0 18px 5px rgba(79, 70, 229, 0.75), 
        0 0 26px 7px rgba(0, 255, 255, 0.6),   
        0 0 34px 9px rgba(60, 179, 113, 0.4);  
    }
  }
  .icon {
    min-width: 16px;
    min-height: 16px;
    max-width: 100%;
    max-height: 100%;
    aspect-ratio: auto;
    object-fit: contain;
  }
</style>
<div class="card-wrapper group relative z-10 m-0.5 xs:m-1">
  <div
    data-url={url}
    class="card flex flex-col h-full bg-white dark:bg-slate-900 relative rounded-xl overflow-hidden shadow-[0_3px_10px_rgba(0,0,0,0.08),_0_1px_3px_rgba(0,0,0,0.05)] dark:shadow-[0_3px_10px_rgba(0,0,0,0.15),_0_1px_3px_rgba(0,0,0,0.1)] transition-all duration-300 ease-in-out border border-[rgba(230,230,230,0.9)] dark:border-[rgba(66,76,94,0.4)]"
    data-category={category}
    data-title={title}
    aria-label={`链接到 ${title} 网站`}
  >
    <div class="card-content p-2 sm:p-3 flex flex-col justify-center">
      <div class="card-header flex items-center mb-1.5">
        <div class="icon-container w-8 h-8 sm:w-10 sm:h-10 mr-2 flex items-center justify-center rounded-full shadow-sm overflow-hidden bg-gradient-to-br from-[#f5f5f5] to-[#e9e9e9] dark:from-[#334155] dark:to-[#1e293b]">
          <img
            src={siteIconFromProps || categoryIconPath || '/images/default.svg'}
            alt={`${title} 网站图标`}
            class="icon w-6 h-6 sm:w-8 sm:h-8 min-w-[85%] min-h-[85%] max-w-[95%] max-h-[95%] object-contain transition-transform duration-300 group-hover:scale-110"
            loading="lazy"
            decoding="async"
            onerror="this.onerror=null; this.src='/images/default.svg';"
            width="24"
            height="24"
            style="aspect-ratio: 1/1;"
          >
        </div>
        <h3 class="title text-xs sm:text-sm font-bold text-gray-900 dark:text-gray-100 m-0 flex-1 truncate">{title}</h3>
      </div>
      <p class="short-desc text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300 m-0 leading-[1.2] line-clamp-2 h-[2.4em] overflow-hidden">{shortDesc}</p>
    </div>
  </div>
  <div class="tooltip absolute bottom-full left-1/2 -translate-x-1/2 translate-y-[10px] w-[120%] max-w-[300px] min-w-[200px] mb-2.5 z-[9999] opacity-0 invisible transition-all duration-300 ease-in-out pointer-events-none group-hover:opacity-100 group-hover:visible group-hover:translate-y-0">
    <div class="tooltip-content relative w-full bg-black/85 backdrop-blur-[10px] text-white p-3 text-sm rounded-xl shadow-[0_10px_25px_rgba(0,0,0,0.2)]">
      <p class="description">{description}</p>
      <div class="tooltip-arrow absolute bottom-[-8px] left-1/2 -translate-x-1/2 w-0 h-0 border-l-[8px] border-l-transparent border-r-[8px] border-r-transparent border-t-[8px] border-t-black/85"></div>
    </div>
  </div>
</div>
<style>
  /* 简短描述样式 - 最多显示两行，超出部分隐藏 */
  .short-desc {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制两行文本 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  /* 网站标题样式 - 最多显示两行 */
  .title {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  /* 描述文本 - 最多显示四行 */
  .description {
    margin: 0; 
    display: -webkit-box;
    -webkit-line-clamp: 4; /* 限制四行文本 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .card-wrapper {
    perspective: 1000px;
  }
</style>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card[data-url]');
    cards.forEach(function(card) {
      card.addEventListener('click', function() {
        const url = card.getAttribute('data-url');
        if (url) {
          window.open(url, '_blank', 'noopener,noreferrer');
        }
      });
    });
  });
</script>
