#!/usr/bin/env node

/**
 * Cloudnav 一键部署脚本
 * 专注于 Cloudflare Workers 纯架构部署
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { createInterface } from 'readline';

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🚀${colors.reset} ${colors.bright}${msg}${colors.reset}`)
};

/**
 * 创建命令行接口
 */
const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 询问用户输入
 * @param {string} question - 问题
 * @returns {Promise<string>}
 */
const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
};

/**
 * 执行命令并显示输出
 * @param {string} command - 命令
 * @param {Object} options - 选项
 * @returns {string}
 */
const execCommand = (command, options = {}) => {
  try {
    log.info(`执行命令: ${command}`);
    const result = execSync(command, {
      encoding: 'utf8',
      stdio: 'inherit',
      ...options
    });
    return result;
  } catch (error) {
    log.error(`命令执行失败: ${command}`);
    log.error(error.message);
    throw error;
  }
};

/**
 * 检查 Workers 部署必要工具
 */
const checkRequirements = () => {
  log.step('检查 Workers 部署环境...');

  const requirements = [
    { command: 'node --version', name: 'Node.js', required: true },
    { command: 'npm --version', name: 'npm', required: true },
    { command: 'wrangler --version', name: 'Wrangler CLI', required: true, autoInstall: true }
  ];

  for (const req of requirements) {
    try {
      const version = execSync(req.command, { encoding: 'utf8', stdio: 'pipe' });
      log.success(`${req.name}: ${version.trim()}`);
    } catch (error) {
      log.error(`${req.name} 未安装或不可用`);

      if (req.autoInstall && req.name === 'Wrangler CLI') {
        log.info('正在自动安装 Wrangler CLI...');
        try {
          execCommand('npm install -g wrangler');
          log.success('Wrangler CLI 安装成功');
        } catch (installError) {
          throw new Error(`Wrangler CLI 安装失败: ${installError.message}`);
        }
      } else {
        throw new Error(`请先安装 ${req.name}`);
      }
    }
  }

  log.success('Workers 部署环境检查完成');
};

/**
 * 检查 Cloudflare 认证
 */
const checkCloudflareAuth = async () => {
  log.step('检查 Cloudflare 认证...');
  
  try {
    execSync('wrangler whoami', { encoding: 'utf8', stdio: 'pipe' });
    log.success('Cloudflare 认证已配置');
  } catch (error) {
    log.warning('Cloudflare 认证未配置');
    log.info('请运行以下命令进行认证:');
    console.log('  wrangler login');
    
    const shouldLogin = await askQuestion('是否现在进行认证? (y/N): ');
    if (shouldLogin.toLowerCase() === 'y') {
      execCommand('wrangler login');
    } else {
      throw new Error('需要 Cloudflare 认证才能继续部署');
    }
  }
};

/**
 * 创建 KV 命名空间
 * @param {string} name - 命名空间名称
 * @returns {string} - 命名空间 ID
 */
const createKVNamespace = (name) => {
  log.info(`创建 KV 命名空间: ${name}`);
  
  try {
    const result = execSync(`wrangler kv:namespace create "${name}"`, {
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    // 解析输出获取 ID
    const match = result.match(/id = "([^"]+)"/);
    if (match) {
      const id = match[1];
      log.success(`KV 命名空间创建成功: ${id}`);
      return id;
    } else {
      throw new Error('无法解析 KV 命名空间 ID');
    }
  } catch (error) {
    log.error(`创建 KV 命名空间失败: ${name}`);
    throw error;
  }
};

/**
 * 更新 wrangler.toml 配置
 * @param {Object} kvIds - KV 命名空间 ID
 */
const updateWranglerConfig = (kvIds) => {
  log.step('更新 wrangler.toml 配置...');
  
  const wranglerPath = 'wrangler.toml';
  if (!existsSync(wranglerPath)) {
    throw new Error('wrangler.toml 文件不存在');
  }
  
  let content = readFileSync(wranglerPath, 'utf8');
  
  // 更新 KV 命名空间 ID
  content = content.replace(
    /id = "your_bookmarks_kv_namespace_id"/,
    `id = "${kvIds.bookmarks}"`
  );
  content = content.replace(
    /id = "your_session_kv_namespace_id"/,
    `id = "${kvIds.session}"`
  );
  
  // 如果有预览 ID，也更新
  if (kvIds.bookmarksPreview) {
    content = content.replace(
      /preview_id = "your_bookmarks_kv_preview_id"/,
      `preview_id = "${kvIds.bookmarksPreview}"`
    );
  }
  if (kvIds.sessionPreview) {
    content = content.replace(
      /preview_id = "your_session_kv_preview_id"/,
      `preview_id = "${kvIds.sessionPreview}"`
    );
  }
  
  writeFileSync(wranglerPath, content);
  log.success('wrangler.toml 配置已更新');
};

/**
 * 构建 Workers 项目
 */
const buildProject = () => {
  log.step('构建 Workers 项目...');

  try {
    // 检查并安装依赖
    if (!existsSync('node_modules')) {
      log.info('检测到缺少依赖，正在安装...');
      execCommand('npm install');
      log.success('依赖安装完成');
    } else {
      log.info('依赖已存在，跳过安装');
    }

    // 清理之前的构建
    if (existsSync('dist')) {
      log.info('清理之前的构建文件...');
    }

    // 执行 Astro 构建
    log.info('执行 Astro 构建，生成 Workers 代码...');
    execCommand('npm run build');

    // 验证构建结果
    if (!existsSync('dist/_worker.js/index.js')) {
      throw new Error('Workers 入口文件未生成，构建可能失败');
    }

    log.success('✅ Workers 项目构建完成');
  } catch (error) {
    log.error('项目构建失败');
    throw new Error(`构建失败: ${error.message}`);
  }
};

/**
 * 部署到 Cloudflare Workers
 */
const deployToWorkers = async () => {
  log.step('部署到 Cloudflare Workers...');

  const environment = await askQuestion('选择部署环境 (development/production) [production]: ');
  const env = environment.trim() || 'production';

  // 验证环境选择
  if (!['development', 'production', 'preview'].includes(env)) {
    log.warning(`无效的环境选择: ${env}，使用默认的 production 环境`);
    env = 'production';
  }

  try {
    log.info(`开始部署到 ${env} 环境...`);

    // 先进行干运行检查
    log.info('执行部署前检查...');
    execCommand(`wrangler deploy --dry-run --env ${env}`);
    log.success('部署前检查通过');

    // 执行实际部署
    log.info('执行实际部署...');
    const deployCommand = `wrangler deploy --env ${env}`;
    execCommand(deployCommand);

    log.success(`🎉 成功部署到 ${env} 环境！`);
    return env;
  } catch (error) {
    log.error(`部署到 ${env} 环境失败`);
    log.error(`错误详情: ${error.message}`);
    throw new Error(`Workers 部署失败: ${error.message}`);
  }
};

/**
 * 初始化数据
 */
const initializeData = async () => {
  log.step('初始化数据...');
  
  const shouldInit = await askQuestion('是否初始化示例数据? (Y/n): ');
  if (shouldInit.toLowerCase() !== 'n') {
    try {
      // 这里可以调用数据初始化脚本
      log.info('正在初始化示例数据...');
      // execCommand('node scripts/init-data.js');
      log.success('示例数据初始化完成');
    } catch (error) {
      log.warning('数据初始化失败，可以稍后手动初始化');
    }
  }
};

/**
 * 显示 Workers 部署结果
 * @param {string} workerUrl - Worker URL
 * @param {string} environment - 部署环境
 */
const showDeploymentResult = (workerUrl, environment = 'production') => {
  console.log('\n' + '='.repeat(70));
  log.success('🎉 Cloudflare Workers 部署完成！');
  console.log('='.repeat(70));

  console.log(`${colors.bright}🚀 部署环境:${colors.reset} ${colors.cyan}${environment}${colors.reset}`);

  if (workerUrl) {
    console.log(`${colors.bright}🌐 网站地址:${colors.reset} ${colors.cyan}${workerUrl}${colors.reset}`);
    console.log(`${colors.bright}📊 管理后台:${colors.reset} ${colors.cyan}${workerUrl}/admin${colors.reset}`);
  }

  console.log(`${colors.bright}📚 项目文档:${colors.reset} ${colors.cyan}https://github.com/arebotx/CloudNav/tree/main/docs${colors.reset}`);

  console.log('\n' + colors.bright + '📋 后续步骤:' + colors.reset);
  console.log('1. 🔐 设置管理员密码 (运行 npm run setup)');
  console.log('2. 📁 导入书签数据');
  console.log('3. 🤖 配置 AI 功能 (可选)');
  console.log('4. 🌐 配置自定义域名 (可选)');

  console.log('\n' + colors.bright + '🔧 常用 Workers 命令:' + colors.reset);
  console.log('• 查看实时日志: wrangler tail');
  console.log('• 更新部署: npm run deploy');
  console.log('• 本地开发: npm run dev');
  console.log('• 查看部署状态: wrangler deployments list');
  console.log('• 管理 KV 数据: wrangler kv:namespace list');

  console.log('\n' + '='.repeat(70));
};

/**
 * 主 Workers 部署流程
 */
const main = async () => {
  let deployedEnvironment = 'production';

  try {
    console.log(`${colors.bright}${colors.magenta}
╔═══════════════════════════════════════╗
║        Cloudnav 一键部署工具          ║
║      纯 Cloudflare Workers 架构       ║
╚═══════════════════════════════════════╝
${colors.reset}`);

    log.info('开始 Cloudflare Workers 部署流程...');

    // 1. 检查 Workers 部署环境
    checkRequirements();

    // 2. 检查 Cloudflare 认证
    await checkCloudflareAuth();

    // 3. 创建 KV 命名空间（Workers 数据存储）
    log.step('创建 Workers KV 命名空间...');
    const kvIds = {
      bookmarks: createKVNamespace('cloudnav-bookmarks'),
      session: createKVNamespace('cloudnav-session')
    };

    // 4. 更新 Workers 配置
    updateWranglerConfig(kvIds);

    // 5. 构建 Workers 项目
    buildProject();

    // 6. 部署到 Workers
    deployedEnvironment = await deployToWorkers();

    // 7. 初始化数据
    await initializeData();

    // 8. 显示部署结果
    const workerUrl = `https://cloudnav-navigation.your-account.workers.dev`;
    showDeploymentResult(workerUrl, deployedEnvironment);

    log.success('🎉 Cloudflare Workers 部署流程完成！');

  } catch (error) {
    log.error('❌ Workers 部署失败:');
    log.error(`错误详情: ${error.message}`);

    console.log('\n' + colors.bright + '🔧 故障排除建议:' + colors.reset);
    console.log('1. 检查网络连接');
    console.log('2. 确认 Cloudflare 账户权限');
    console.log('3. 验证 wrangler.toml 配置');
    console.log('4. 查看详细日志: wrangler tail');

    process.exit(1);
  } finally {
    rl.close();
  }
};

// 运行主流程
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
