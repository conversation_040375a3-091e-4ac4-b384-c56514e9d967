# Cloudflare Workers 环境变量模板
# 复制此文件为 .env 并填入实际值

# Gemini AI API Key (可选功能)
# 从 https://ai.google.dev/ 获取
GEMINI_API_KEY=your_gemini_api_key_here

# 管理功能开关
ENABLE_ADMIN=true

# 管理员密码 (可选，用于简单认证)
ADMIN_PASSWORD=your_admin_password_here

# Cloudflare KV 命名空间 ID
# 需要在 Cloudflare 控制台中创建 KV 命名空间并获取 ID
# 然后在 wrangler.toml 中替换 your_kv_namespace_id
# KV_NAMESPACE_ID=your_actual_kv_namespace_id

# 站点配置
SITE_URL=https://your-domain.com

# 开发环境配置
NODE_ENV=development
