# Cloudflare Workers 环境变量模板
# 复制此文件为 .env 并填入实际值

# Gemini AI API Key (可选功能)
# 从 https://ai.google.dev/ 获取
GEMINI_API_KEY=your_gemini_api_key_here

# 管理功能开关
ENABLE_ADMIN=true

# 管理员密码 (可选，用于简单认证)
ADMIN_PASSWORD=your_admin_password_here

# Cloudflare KV 统一存储配置
# 所有数据（书签、分类、会话、统计等）都存储在同一个 KV 命名空间中
# 运行 npm run setup 自动创建 KV 命名空间并更新 wrangler.toml
# CLOUDNAV_KV_NAMESPACE_ID=your_actual_kv_namespace_id

# 站点配置
SITE_URL=https://your-domain.com

# 开发环境配置
NODE_ENV=development
