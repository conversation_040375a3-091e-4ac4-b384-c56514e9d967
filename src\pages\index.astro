---
import MainLayout, { siteTitle, siteDescription } from '../layouts/MainLayout.astro'; 
import Header from '../components/Header.astro';
import Sidebar from '../components/Sidebar.astro';
import Card from '../components/Card.astro';
import Footer from '../components/Footer.astro';
import HNavbar from '../components/HNavbar.astro';
import { categories, sites } from '../data/navLinks.js';
const pageTitle = siteTitle;
const pageDescription = siteDescription;
---
<MainLayout
  title={pageTitle}
  description={pageDescription}
>
  <Header />
  <div class="container mx-auto px-4 lg:flex lg:flex-nowrap lg:gap-6">
    <Sidebar class="lg:block lg:sticky lg:top-0 lg:h-screen lg:flex-none lg:w-60 lg:overflow-y-auto lg:translate-x-0 lg:z-auto" />
    <main class="pt-24 pb-8 lg:flex-1 lg:min-w-0">
      <h1 class="sr-only">{pageTitle}</h1> 
      <HNavbar />
      {categories.map(category => (
        <section class="mb-10" id={category.id}>
          <h2 class="category-title text-2xl font-bold mb-4 pb-2 border-b border-gray-200 dark:border-gray-700 flex items-center">
            <div class="w-8 h-8 mr-3 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
              <img src={category.icon} alt={`${category.name} 分类图标`} class="w-5 h-5" width="20" height="20" />
            </div>
            {category.name}
          </h2>
          <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-2 sm:gap-6">
            {sites
              .filter(site => site.category === category.id)
              .map(site => (
                <Card 
                  title={site.title}
                  description={site.description}
                  shortDesc={site.shortDesc}
                  url={site.url}
                  icon={site.icon}
                  category={site.category}
                />
              ))
            }
          </div>
        </section>
      ))}
    </main>
  </div>
  <Footer />
</MainLayout>
<script>
  const searchInput = document.getElementById('search-input') as HTMLInputElement;
  const searchResults = document.getElementById('search-results');
  const allCards = document.querySelectorAll('.card');
  interface SearchResult {
    title: string;
    description: string;
    categoryId: string;
    categoryName: string;
    url: string;
    icon: string;
  }
  function getCategoryNameById(categoryId: string): string {
    const categoryElement = document.querySelector(`section#${categoryId} h2`);
    return categoryElement && categoryElement.textContent ? categoryElement.textContent.trim() : categoryId;
  }
  function initSearch(): void {
    if (!searchInput) return;
    searchInput.addEventListener('input', (e: Event) => {
      const target = e.target as HTMLInputElement;
      const query = target?.value?.toLowerCase() || '';
      if (!query) {
        searchResults?.classList.add('hidden');
        return;
      }
      const results: SearchResult[] = [];
      allCards.forEach(card => {
        const title = card.querySelector('h3')?.textContent?.toLowerCase() || '';
        const description = card.querySelector('p')?.textContent?.toLowerCase() || '';
        const categoryId = card.getAttribute('data-category') || '';
        const url = card.getAttribute('data-url') || '#';
        const icon = card.querySelector('img')?.getAttribute('src') || '/icons/default.svg';
        if (title.includes(query) || description.includes(query)) {
          const categoryName = getCategoryNameById(categoryId);
          results.push({
            title: card.querySelector('h3')?.textContent || '',
            description: card.querySelector('p')?.textContent || '',
            categoryId,
            categoryName,
            url,
            icon
          });
        }
      });
      if (results.length > 0) {
        searchResults?.classList.remove('hidden');
        let resultsHTML = '';
        results.forEach(result => {
          resultsHTML += `
            <div class="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer flex items-start">
              <div class="w-10 h-10 mr-3 flex-shrink-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-md overflow-hidden">
                <img src="${result.icon}" alt="${result.title} 图标" class="w-6 h-6" width="24" height="24" loading="lazy" onerror="this.src='/icons/default.svg'">
              </div>
              <div class="flex-grow">
                <h4 class="font-medium text-gray-900 dark:text-gray-100">${result.title}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">${result.description}</p>
                <span class="text-xs text-gray-500 dark:text-gray-500 mt-1">分类: ${result.categoryName}</span>
              </div>
            </div>
          `;
        });
        if (searchResults) {
          searchResults.innerHTML = resultsHTML;
          const resultItems = searchResults.querySelectorAll('div.cursor-pointer');
          resultItems.forEach((item, index) => {
            item.addEventListener('click', () => {
              const result = results[index];
              if (result && result.url) {
                window.location.href = result.url;
              }
            });
          });
        }
      } else {
        searchResults?.classList.remove('hidden');
        if (searchResults) {
          searchResults.innerHTML = `<div class="p-3 text-center text-gray-500 dark:text-gray-400">没有找到相关网站</div>`;
        }
      }
    });
  }
  function initClickOutside(): void {
    document.addEventListener('click', (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target && (!target.closest('#search-container') && !target.closest('#search-toggle'))) {
        searchResults?.classList.add('hidden');
      }
    });
  }
  initSearch();
  initClickOutside();
</script>
