<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 滤镜定义 - 添加发光和内阴影效果 -->
  <defs>
    <!-- 背景渐变 - 增强左上至右下的立体感 -->
    <linearGradient id="bgGradient" x1="50" y1="50" x2="462" y2="462" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3b82f6" /> <!-- 浅蓝色起点 -->
      <stop offset="0.3" stop-color="#2563EB" /> <!-- 蓝色 -->
      <stop offset="0.6" stop-color="#4F46E5" /> <!-- 紫色 -->
      <stop offset="1" stop-color="#7C3AED" /> <!-- 深紫色终点 -->
    </linearGradient>
    
    <!-- 内部柔光效果 -->
    <radialGradient id="innerGlow" cx="40%" cy="40%" r="60%" fx="40%" fy="40%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.25" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
    </radialGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="140" y1="240" x2="280" y2="300" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffffff" />
      <stop offset="1" stop-color="#e2e8f0" />
    </linearGradient>
    
    <!-- 边缘发光滤镜 -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="8" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- 内阴影滤镜 -->
    <filter id="innerShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset dx="0" dy="4" />
      <feGaussianBlur stdDeviation="6" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
      <feDropShadow dx="0" dy="-2" stdDeviation="4" flood-color="#000000" flood-opacity="0.2" />
    </filter>
    
    <!-- 导航指针发光效果 -->
    <filter id="pointerGlow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="3" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
      <feDropShadow dx="0" dy="0" stdDeviation="5" flood-color="#ffffff" flood-opacity="0.5" />
    </filter>
    
    <!-- 连接点发光效果 -->
    <filter id="dotGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 背景圆 - 应用渐变和内阴影 -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#innerShadow)" />
  
  <!-- 内发光效果层 -->
  <circle cx="256" cy="256" r="220" fill="url(#innerGlow)" opacity="0.7" />
  
  <!-- 地球网格线 - 改进的视觉效果 -->
  <g opacity="0.25" filter="url(#glow)">
    <path d="M256 16C256 16 400 100 400 256C400 412 256 496 256 496" stroke="white" stroke-width="2" />
    <path d="M256 16C256 16 112 100 112 256C112 412 256 496 256 496" stroke="white" stroke-width="2" />
    <ellipse cx="256" cy="256" rx="180" ry="240" stroke="white" stroke-width="2" />
    <ellipse cx="256" cy="256" rx="120" ry="240" stroke="white" stroke-width="2" />
    <ellipse cx="256" cy="256" rx="60" ry="240" stroke="white" stroke-width="2" />
    <circle cx="256" cy="256" r="240" stroke="white" stroke-width="2" />
    <line x1="16" y1="256" x2="496" y2="256" stroke="white" stroke-width="2" />
    <line x1="16" y1="176" x2="496" y2="176" stroke="white" stroke-width="2" />
    <line x1="16" y1="336" x2="496" y2="336" stroke="white" stroke-width="2" />
  </g>
  
  <!-- 互联网连接点 - 增加发光效果 -->
  <circle cx="180" cy="180" r="8" fill="white" filter="url(#dotGlow)" />
  <circle cx="320" cy="200" r="8" fill="white" filter="url(#dotGlow)" />
  <circle cx="220" cy="300" r="8" fill="white" filter="url(#dotGlow)" />
  <circle cx="350" cy="320" r="8" fill="white" filter="url(#dotGlow)" />
  <circle cx="150" cy="350" r="8" fill="white" filter="url(#dotGlow)" />
  
  <!-- 连接线 - 增加柔和发光 -->
  <g filter="url(#glow)" opacity="0.9">
    <line x1="180" y1="180" x2="320" y2="200" stroke="white" stroke-width="2" />
    <line x1="320" y1="200" x2="220" y2="300" stroke="white" stroke-width="2" />
    <line x1="220" y1="300" x2="350" y2="320" stroke="white" stroke-width="2" />
    <line x1="350" y1="320" x2="150" y2="350" stroke="white" stroke-width="2" />
    <line x1="150" y1="350" x2="180" y2="180" stroke="white" stroke-width="2" />
  </g>
  
  <!-- ZYWE文字 - 使用渐变和阴影增强立体感 -->
  <g transform="translate(140, 240)" filter="url(#innerShadow)">
    <!-- Z -->
    <path d="M0 0H60V10L20 50H60V60H0V50L40 10H0V0Z" fill="url(#textGradient)" />
    <!-- Y -->
    <path d="M70 0H80L100 25L120 0H130L105 30V60H95V30L70 0Z" fill="url(#textGradient)" />
    <!-- W -->
    <path d="M140 0H150L165 40L180 10L195 40L210 0H220L200 60H190L180 35L170 60H160L140 0Z" fill="url(#textGradient)" />
    <!-- E -->
    <path d="M230 0H280V10H240V25H270V35H240V50H280V60H230V0Z" fill="url(#textGradient)" />
  </g>
  
  <!-- 导航指针元素 - 添加发光效果 -->
  <path d="M256 110L290 160H256L240 190L222 160H256L256 110Z" fill="url(#textGradient)" filter="url(#pointerGlow)" />
</svg>
